.ext-wikisource-bulkocr-container {
	margin-bottom: 1em;
	margin-top: 1em;
	border: 1px dashed #808080;
	padding: 8px;
}

.ext-wikisource-bulkocr-buttongroup {
	margin-bottom: 0.5em;
}

.ext-wikisource-bulkocr-button {
	display: inline-block;
}

.oo-ui-popupButtonWidget {
		border-left-style: ;
	}

/* Styles for the config dropdown */
.ext-wikisource-ocr-config-panel {
	display: flex;
	flex-direction: column;
	align-items: stretch;
	overflow: scroll;

	.ext-wikisource-ocr-engine-label,
	.ext-wikisource-ocr-engineradios .oo-ui-radioOptionWidget {
		display: flex;
		align-items: center;
		padding: 12px 12px 4px 12px;

		.oo-ui-inputWidget,
		.ext-wikisource-icon-wrapper {
			width: 20px;
		}

		.oo-ui-labelElement-label {
			padding-left: 12px;
		}
	}

	.ext-wikisource-ocr-engineradios {
		font-weight: bolder;
		margin-bottom: 12px;
	}

	.oo-ui-fieldsetLayout {
		padding: 0 0 12px 36px;
	}

	.ext-wikisource-ocr-more-options {
		margin-top: 0.35em;
		border-top: 1px solid #eaecf0;
		padding: 0.75em 0.35em 1.25em 0.3em;
	}
}

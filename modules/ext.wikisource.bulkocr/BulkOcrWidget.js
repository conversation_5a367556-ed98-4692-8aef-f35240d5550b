class BulkOcrWidget {
	constructor( pageCount, ocrStartIndex ) {
		// Create the Bulk OCR button, Warning message, and UI Container
		const bulkOcrButton = new OO.ui.ButtonWidget( {
			icon: 'bulk-ocr',
			label: mw.msg( 'wikisource-bulkocr-button' ),
			classes: [ 'ext-wikisource-bulkocr-button' ]
		} );
		bulkOcrButton.$icon.addClass( 'ext-wikisource-icon-bulk-ocr' );

		// Create config button with dropdown
		this.configButton = new OO.ui.PopupButtonWidget( {
			indicator: 'down',
			title: mw.message( 'wikisource-ocr-settings-menu' ).text(),
			invisibleLabel: true,
			classes: [ 'ext-wikisource-bulkocr-config' ],
			$overlay: OO.ui.getDefaultOverlay(),
			popup: {
				anchor: false,
				$content: this.getConfigContent().$element,
				padded: false,
				align: 'force-left'
			}
		} );
		// Replace the built-in click handler with our own
		this.configButton.disconnect( this.configButton, { click: 'onAction' } );
		this.configButton.connect( this, { click: 'onClickConfigButton' } );

		const warningMessage = new OO.ui.MessageWidget( {
			type: 'warning',
			label: mw.msg( 'wikisource-bulkocr-active-development-warning' ),
			classes: [ 'ext-wikisource-bulkocr-warning' ]
		} );
		const container = document.createElement( 'div' );
		container.className = 'ext-wikisource-bulkocr-container';

		// Create button group for OCR button and config button
		const buttonGroup = new OO.ui.ButtonGroupWidget( {
			items: [ bulkOcrButton, this.configButton ],
			classes: [ 'ext-wikisource-bulkocr-buttongroup' ]
		} );

		container.appendChild( buttonGroup.$element[ 0 ] );
		container.appendChild( warningMessage.$element[ 0 ] );
		bulkOcrButton.on( 'click', () => {
			this.executeBulkOcr();
		} );

		this.bulkOcrButton = bulkOcrButton;
		this.warningMessage = warningMessage;
		this.container = container;
		this.buttonGroup = buttonGroup;

		// Store individual widget-specific data
		this.pageCount = pageCount;
		this.ocrStartIndex = ocrStartIndex;
		this.ocrEndIndex = this.ocrStartIndex + this.pageCount;

		// Initialize state variables
		this.ocrDictionary = {}; // Maps page titles to OCR text
		this.selectedEngine = 'google'; // Default OCR engine
		this.selectedLanguageKey = 'eng'; // Default language

		// While the PopupWidget.toggle() closes the popup,
		// the RadioSelectWidget.onDocumentKeyDown() method retains focus on the
		// radio buttons and allows the arrow keys to change the selected engine,
		// even when the popup is closed
		// So, disable the RadioSelect widget when the popup is closed
		this.configButton.popup.connect( this, { closing: () => {
			if ( this.radioSelect ) {
				this.radioSelect.setDisabled( true );
			}
		} } );
		this.mwApi = new mw.Api();

		// Create shared notification element
		this.progressNotificationElement = document.createElement( 'div' );
		this.progressNotificationId = null;

		// Set up event emitter
		this.events = $( {} );
	}

	/**
	 * Get the configuration content for the dropdown
	 *
	 * @return {OO.ui.PanelLayout} The panel containing configuration options
	 */
	getConfigContent() {
		let ocrOptions = [
			new OO.ui.RadioOptionWidget( {
				data: 'tesseract',
				label: mw.msg( 'wikisource-ocr-engine-tesseract' )
			} ),
			new OO.ui.RadioOptionWidget( {
				data: 'google',
				label: mw.msg( 'wikisource-ocr-engine-google' )
			} ),
			new OO.ui.RadioOptionWidget( {
				data: 'transkribus',
				label: mw.msg( 'wikisource-ocr-engine-transkribus' )
			} )
		];

		this.moreOptionsFieldset = new OO.ui.FieldsetLayout( {
			classes: [ 'ext-wikisource-ocr-more-options' ]
		} );

		// create fieldset for line detection checkbox
		this.fieldset = new OO.ui.FieldsetLayout( {} );
		this.lineDetectionCheckBox = new OO.ui.CheckboxInputWidget( {
			selected: false
		} );
		this.fieldset.addItems( [
			new OO.ui.FieldLayout( this.lineDetectionCheckBox, { label: mw.msg( 'wikisource-ocr-engine-line-model-checkbox-label' ), align: 'inline' } )
		] );
		this.lineDetectionCheckBox.connect( this, {
			change: 'setLineDetectionModel'
		} );

		this.fieldset.toggle( this.selectedEngine === 'transkribus' );
		this.radioSelect = new OO.ui.RadioSelectWidget( {
			classes: [ 'ext-wikisource-ocr-engineradios' ],
			items: ocrOptions
		} );
		var label = new OO.ui.LabelWidget( {
			classes: [ 'ext-wikisource-ocr-engine-label' ],
			label: mw.msg( 'wikisource-ocr-engine' ),
			input: this.radioSelect
		} );
		this.radioSelect.connect( this, {
			choose: 'onEngineChoose'
		} );
		this.radioSelect.selectItemByData( this.selectedEngine );

		this.updateMoreOptionsFields();
		var content = new OO.ui.PanelLayout( {
			padded: false,
			expanded: false,
			classes: [ 'ext-wikisource-ocr-config-panel' ]
		} );
		content.$element.append(
			label.$element,
			this.radioSelect.$element,
			this.fieldset.$element,
			this.moreOptionsFieldset.$element
		);

		return content;
	}

	/**
	 * Handle click on the config button
	 */
	onClickConfigButton() {
		// Replicate the behaviour from OO.ui.PopupButtonWidget.prototype.onAction
		this.configButton.popup.toggle();

		// Enable the radio select widget based on popup visibilty
		this.radioSelect.setDisabled( !this.configButton.popup.isVisible() );
		// Focus the radio button on clicking the config button
		// allowing the option to be changed by arrow keys on the keyboard
		this.radioSelect.focus();
	}

	/**
	 * Handle engine selection change
	 *
	 * @param {OO.ui.OptionWidget} item Chosen item
	 * @param {boolean} selected Item is selected
	 */
	onEngineChoose( item, selected ) {
		if ( selected ) {
			this.selectedEngine = item.data;
			// enable the checkbox for line detection only if the
			// selected engine is Transkribus
			this.lineDetectionCheckBox.setDisabled( item.data !== 'transkribus' );
			this.fieldset.toggle( item.data === 'transkribus' );

			this.updateMoreOptionsFields();
		}
	}

	/**
	 * On changing the line detection model checkbox selection.
	 */
	setLineDetectionModel() {
		// This is a placeholder implementation since we don't have ocrTool
		if ( this.lineDetectionCheckBox.selected ) {
			console.log( 'Line detection disabled' );
		} else {
			console.log( 'Line detection enabled' );
		}
	}

	/**
	 * Update the more options fields based on the selected engine
	 */
	updateMoreOptionsFields() {
		let engine = this.selectedEngine;
		this.moreOptionsFieldset.clearItems();
		let options = this.getLanguages( engine );
		let fieldLabel = this.setLanguageDropdownLabel( engine );

		if ( engine !== 'transkribus' ) {
			this.languageDropdown = new OO.ui.MenuTagMultiselectWidget( {
				options: options
			} );
			this.languageDropdown.connect( this, {
				change: 'setLanguages'
			} );
			// Find the option that matches our selected language key
			const selectedOption = options.find( option => option.data === this.selectedLanguageKey );
			if ( selectedOption ) {
				this.languageDropdown.addTag( selectedOption.data, selectedOption.label );
			}
		} else {
			this.languageDropdown = new OO.ui.DropdownInputWidget( {
				options: options
			} );
			this.languageDropdown.connect( this, {
				change: 'setModel'
			} );
			this.languageDropdown.setValue( this.selectedLanguageKey );
		}

		let dropdowns = [
			new OO.ui.FieldLayout( this.languageDropdown, { label: fieldLabel, align: 'inline' } )
		];

		this.moreOptionsFieldset.addItems( dropdowns );
	}

	/**
	 * Set the language dropdown label based on the engine
	 *
	 * @param {string} engine The selected OCR engine
	 * @return {string} The label for the language dropdown
	 */
	setLanguageDropdownLabel( engine ) {
		let fieldLabel = mw.msg( 'wikisource-ocr-language-dropdown-label' );
		if ( engine === 'transkribus' ) {
			fieldLabel = mw.msg( 'wikisource-ocr-model-dropdown-label' );
		}
		return fieldLabel;
	}

	/**
	 * Get the available languages for the selected engine
	 *
	 * @param {string} engine The selected OCR engine
	 * @return {Array} The available languages
	 */
	getLanguages( engine ) {
		// Simplified implementation since we don't have ocrTool.allLangs
		let items = [];
		let languages = {
			eng: 'English',
			fra: 'French',
			deu: 'German',
			spa: 'Spanish',
			ita: 'Italian',
			por: 'Portuguese'
		};

		for ( let key in languages ) {
			items.push( {
				data: key,
				label: languages[ key ]
			} );
		}

		return items;
	}

	/**
	 * Get the label for a language code
	 *
	 * @param {string} code The language code
	 * @return {string} The language label
	 */
	getLanguageLabel( code ) {
		const languages = {
			eng: 'English',
			fra: 'French',
			deu: 'German',
			spa: 'Spanish',
			ita: 'Italian',
			por: 'Portuguese'
		};
		return languages[ code ] || code;
	}

	/**
	 * Set the selected languages
	 *
	 * @param {Array} items The selected language items
	 */
	setLanguages( items ) {
		let langs = [];
		items.forEach( element => {
			langs.push( element.data );
		} );
		this.selectedLanguageKey = langs[ 0 ] || 'eng';
	}

	/**
	 * Set the selected model
	 *
	 * @param {string} item The selected model
	 */
	setModel( item ) {
		this.selectedLanguageKey = item;
	}

	/**
	 * Execute the bulk OCR process for untranscribed pages in the current Index namespace
	 * 1. Finds untranscribed pages in the current Index namespace
	 * 2. Retrieves images for those pages
	 * 3. Processes OCR on the images in batches
	 * 4. Saves the OCR results to the pages in batches
	 * 5. Refreshes the page when complete
	 */
	executeBulkOcr() {
		// Get the current Index URL title
		const title = mw.config.get( 'wgPageName' );
		if ( !title ) {
			return;
		}

		// Reset OCR dictionary
		this.ocrDictionary = {};

		// Reset event handlers
		this.events.off( 'ocr-pages-found ocr-images-loaded ocr-complete update-pages-complete' );

		// Handle when untranscribed pages are found
		this.events.on( 'ocr-pages-found', ( e, indexTitle, titlesArray ) => {
			this.getImagesForPages( indexTitle, titlesArray );
		} );

		// Handle when images are loaded
		this.events.on( 'ocr-images-loaded', ( e, pageImageMap ) => {
			// Process OCR in batches of 10
			this.processBatchedOcr( pageImageMap, 10 )
				.then( () => {
					this.events.trigger( 'ocr-complete' );
				} );
		} );

		// Handle when OCR processing is complete
		this.events.on( 'ocr-complete', () => {
			this.saveOcrResults();
		} );

		// Handle when page updates are complete
		this.events.on( 'update-pages-complete', () => {
			this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-success-message' );

			this.progressNotificationId = mw.notify( $( this.progressNotificationElement ), {
				autoHide: true,
				autoHideSeconds: 30,
				type: 'success',
				tag: 'bulk-ocr-progress'
			} );

			this.updatePageListStatus();
		} );

		// Start the process
		this.getUnTranscribedPagesInIndex( title )
			.then( titlesArray => {
				if ( titlesArray.length === 0 ) {
					// Close shared notification element when no pages are found
					this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-in-progress' );
					mw.notify( $( this.progressNotificationElement ), {
						autoHide: true,
						type: 'info',
						tag: 'bulk-ocr-progress'
					} );
					mw.notify( mw.msg( 'wikisource-bulkocr-no-pages-found' ), { type: 'error' } );
					return;
				}
				this.events.trigger( 'ocr-pages-found', [ title, titlesArray ] );
			} )
			.catch( error => {
				console.error( 'Error in bulk OCR process:', error );
			} );
	}

	/**
	 * Get UnTranscribed pages from Current Index namespace
	 *
	 * @param {string} indexTitle - The title of the index page
	 * @return {jQuery.Promise} - Promise resolving to array of untranscribed page titles
	 */
	getUnTranscribedPagesInIndex( indexTitle ) {
		const deferred = $.Deferred();

		this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-in-progress' );

		this.progressNotificationId = mw.notify( $( this.progressNotificationElement ), {
			autoHide: false,
			type: 'info',
			tag: 'bulk-ocr-progress'
		} );

		this.mwApi.get( {
			action: 'query',
			list: 'proofreadpagesinindex',
			prppiititle: indexTitle,
			prppiiprop: 'ids|title|formattedpagenumber',
			formatversion: 2
		} ).done( ( response ) => {
			const unTranscribedPages = [];
			if ( response.query.proofreadpagesinindex ) {
				const allPages = response.query.proofreadpagesinindex;
				const currentPagelistPages = allPages.slice( this.ocrStartIndex, this.ocrEndIndex );

				// Collect uncreated pages (pageid === 0)
				currentPagelistPages.forEach( page => {
					if ( page.pageid === 0 ) {
						unTranscribedPages.push( page.title );
					}
				} );
			}
			deferred.resolve( unTranscribedPages );
		} ).fail( ( xhr, status, error ) => {
			mw.notify( mw.msg( 'wikisource-bulkocr-fetch-pages-failed' ), { type: 'error' } );
			deferred.reject( error || 'API request failed' );
		} );

		return deferred.promise();
	}

	/**
	 * Get images for pages in the index
	 *
	 * @param {string} indexTitle - The title of the index page
	 * @param {Array} titlesArray - Array of page titles to get images for
	 */
	getImagesForPages( indexTitle, titlesArray ) {
		const pageImageMap = {};
		const languageCode = mw.config.get( 'wgContentLanguage' );

		$.ajax( {
			url: `https://${languageCode}.wikisource.org/w/api.php`,
			data: {
				action: 'query',
				prop: 'imageforpage',
				generator: 'proofreadpagesinindex',
				formatversion: 2,
				prppifpprop: 'filename|size|fullsize|responsiveimages',
				gprppiiprop: 'ids|title',
				gprppiititle: indexTitle,
				format: 'json',
				origin: '*'
			},
			dataType: 'json' } ).done( ( imgResponse ) => {
			if ( imgResponse.query && imgResponse.query.pages ) {
				Object.values( imgResponse.query.pages ).forEach( page => {
					const pageTitle = page.title;
					// Remove already transcribed or non-empty pages
					if ( !titlesArray.includes( pageTitle ) ) {
						return;
					}
					const thumbnail = page.imagesforpage.thumbnail || '';
					// add image to dictionary
					if ( thumbnail ) {
						pageImageMap[ pageTitle ] = thumbnail;
					}
				} );
			}
			this.events.trigger( 'ocr-images-loaded', [ pageImageMap ] );
		} ).fail( ( xhr, status, error ) => {
			console.error( 'Image data fetch failed:', error );
			mw.notify( mw.msg( 'wikisource-bulkocr-fetch-images-failed' ), { type: 'error' } );
		} );
	}

	/**
	 * Process OCR for a single page
	 *
	 * @param {string} pageTitle - The title of the page
	 * @param {string} thumbnail - The thumbnail URL for the page
	 * @return {jQuery.Promise} - Promise resolving to the OCR text or null if failed
	 */
	processOcrForPage( pageTitle, thumbnail ) {
		const deferred = $.Deferred();
		const toolUrl = mw.config.get( 'WikisourceOcrUrl' );
		// Use the selected engine and language from the dropdown
		let langs = Array.isArray( this.selectedLanguageKey ) ? this.selectedLanguageKey : [ this.selectedLanguageKey ];
		let langsParam = langs.map( lang => `langs%5B%5D=${lang}` ).join( '&' );
		const ocrApiUrl = `${toolUrl}/api?engine=${this.selectedEngine}&image=${encodeURIComponent( thumbnail )}&${langsParam}`;

		$.ajax( {
			url: ocrApiUrl,
			dataType: 'json',
			success: ( ocrResponse ) => {
				if ( ocrResponse.text ) {
					deferred.resolve( ocrResponse.text );
				} else {
					deferred.resolve( null );
				}
			},
			error: ( xhr, status, error ) => {
				mw.notify( `Failed to process OCR for ${pageTitle}`, { type: 'error' } );
				console.error( `OCR failed for ${pageTitle}:`, error );
				deferred.reject( error );
			}
		} );

		return deferred.promise();
	}

	/**
	 * Process OCR for pages in batches
	 *
	 * @param {Object} pageImageMap - Map of page titles to thumbnail URLs
	 * @param {number} batchSize - Number of requests to process in each batch
	 * @return {jQuery.Promise} - Promise resolving when all batches are complete
	 */
	processBatchedOcr( pageImageMap, batchSize = 10 ) {
		const deferred = $.Deferred();
		const entries = Object.entries( pageImageMap );
		const totalPages = entries.length;
		let processedCount = 0;

		this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-ocr-progress', 0, totalPages );

		// Show the notification if not already shown
		if ( !this.progressNotificationId ) {
			this.progressNotificationId = mw.notify( $( this.progressNotificationElement ), {
				autoHide: false,
				type: 'info',
				tag: 'bulk-ocr-progress'
			} );
		}

		const processBatch = ( startIndex ) => {
			const batch = entries.slice( startIndex, startIndex + batchSize );
			if ( batch.length === 0 ) {
				deferred.resolve();
				return;
			}

			const batchPromises = batch.map( ( [ pageTitle, thumbnail ] ) => {
				return this.processOcrForPage( pageTitle, thumbnail )
					.then( ocrText => {
						if ( ocrText ) {
							this.ocrDictionary[ pageTitle ] = ocrText;
						}
						processedCount++;
						// Update notification text
						this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-ocr-progress', processedCount, totalPages );
						return ocrText;
					} );
			} );

			$.when.apply( $, batchPromises )
				.always( () => {
					// Process next batch after a short delay to avoid rate limiting
					setTimeout( () => {
						processBatch( startIndex + batchSize );
					}, 1000 );
				} );
		};

		// Start processing the first batch
		processBatch( 0 );

		return deferred.promise();
	}

	/**
	 * Save OCR results to pages in batches
	 *
	 * @param {number} batchSize - Number of saves to process in each batch
	 */
	saveOcrResults( batchSize = 10 ) {
		const entries = Object.entries( this.ocrDictionary );
		const totalEntries = entries.length;
		let savedCount = 0;
		let failedPages = [];

		// Update shared notification element
		this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-saving-progress', 0, totalEntries );

		const saveBatch = ( startIndex ) => {
			const batch = entries.slice( startIndex, startIndex + batchSize );
			if ( batch.length === 0 ) {
				this.events.trigger( 'update-pages-complete' );
				return;
			}

			let batchPendingSaves = batch.length;

			batch.forEach( ( [ pageTitle, text ] ) => {
				this.mwApi.postWithToken( 'csrf', {
					action: 'edit',
					title: pageTitle,
					appendtext: text, // Append OCR text to page
					createonly: 1, // Only create new pages, don't overwrite existing content
					format: 'json'
				} ).done( () => {
					savedCount++;
					// Update notification text
					this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-saving-progress', savedCount, totalEntries );
					if ( failedPages.length > 0 ) {
						this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-saving-progress-with-failures', savedCount, totalEntries, failedPages.join( ', ' ) );
					}

					batchPendingSaves--;
					if ( batchPendingSaves === 0 ) {
						// Process next batch after a short delay
						setTimeout( () => {
							saveBatch( startIndex + batchSize );
						}, 1000 );
					}
				} ).fail( ( error ) => {
					console.error( `Edit failed for ${pageTitle}:`, error );
					failedPages.push( pageTitle );

					// Update notification to show failure
					this.progressNotificationElement.textContent = mw.msg( 'wikisource-bulkocr-saving-progress-with-failures', savedCount, totalEntries, failedPages.join( ', ' ) );

					batchPendingSaves--;
					if ( batchPendingSaves === 0 ) {
						// Process next batch after a short delay
						setTimeout( () => {
							saveBatch( startIndex + batchSize );
						}, 1000 );
					}
				} );
			} );
		};

		// Start saving the first batch
		saveBatch( 0 );
	}

	/**
	 * Live Updates pageList links from "page does not exist"
	 * status to "not proofread" status by updating CSS classes.
	 */
	updatePageListStatus() {
		// Update pagelist links to show 'not proofread' status
		Object.keys( this.ocrDictionary ).forEach( pageTitle => {
			const pageLink = document.querySelector( `a[title="${pageTitle} (page does not exist)"]` );
			if ( pageLink ) {
				pageLink.classList.remove( 'new' );
				pageLink.classList.add( 'prp-pagequality-1', 'quality1' );
			}
		} );
	}

}

module.exports = BulkOcrWidget;
